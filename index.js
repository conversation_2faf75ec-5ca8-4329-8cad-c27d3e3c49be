BigInt.prototype.toJSON = function () {
  return this.toString();
};

// Include general modules
const express = require("express");
const bodyParser = require("body-parser");
const firebaseAdmin = require("firebase-admin");
const config = require("./config/config.js");
require("log-timestamp");
const AWS = require("aws-sdk");

const mysql = require("./mysql");
mysql.createConnectionPool();

AWS.config.loadFromPath("./config/config-aws.json");
//AWS.config.update({region: 'us-east-1'});
const s3 = new AWS.S3({ apiVersion: "2006-03-01" });

// Including custom modules
const logMiddleware = require("./middlewares/logMiddleware");

// Initialize app express
const app = express();
app.use(bodyParser.urlencoded({ limit: "50mb", extended: true }));
app.use(bodyParser.json({ limit: "50mb" }));

// Initial firebase and connect to it.
firebaseAdmin.initializeApp({
  credential: firebaseAdmin.credential.cert(config.firebase.cert_path),
  databaseURL: config.firebase.database_url,
});
const db = firebaseAdmin.database();

var rootRef = db.ref();

/**
 * Test endpoint. Easy to test that script is running and that
 * ports are opened and that its reachable.
 */
app.get("/test", (req, res) => {
  console.log("Test request received!");
  res.send({
    "test-reply": "test-reply",
  });
});

app.post("/removeFile", (req, res) => {
  let dataIn = req.body;

  console.log("removeFile | ", dataIn);

  if (
    dataIn.hasOwnProperty("bucketName") &&
    dataIn.bucketName != "" &&
    dataIn.hasOwnProperty("url") &&
    dataIn.url != ""
  ) {
    var url = dataIn.url;
    url = url.split("/");
    var arrLength = url.length;
    var keyFileName = "";

    if (dataIn.bucketName == "ultimate-recordings") {
      keyFileName = url[arrLength - 2] + "/" + url[arrLength - 1];
    } else {
      keyFileName =
        url[arrLength - 4] +
        "/" +
        url[arrLength - 3] +
        "/" +
        url[arrLength - 2] +
        "/" +
        url[arrLength - 1];
    }

    var bucketParams = {
      Bucket: dataIn.bucketName,
      Key: keyFileName,
    };

    // Call S3 to create the bucket
    s3.deleteObject(bucketParams, function (err, data) {
      if (data) {
        res.send({
          status: "success",
          message: "File remove successfully",
          data: data,
        });
      } else {
        res.send({ status: "error", message: "Error Occured", error: err });
      }
    });
  } else {
    res.send({ status: "error", message: "Please enter all required fields" });
  }
});

const reverseObj = (obj) => {
  let newObj = {};

  Object.keys(obj)
    .reverse()
    .forEach((key) => {
      // console.log(key)
      newObj[key] = obj[key];
    });
  return newObj;
};

app.post("/getData", (req, res) => {
  let dataIn = req.body;

  console.log("getData | ", dataIn);

  if (dataIn.hasOwnProperty("getFrom") && dataIn.getFrom != "") {
    var receivedRef = rootRef.child(dataIn.getFrom),
      orderByChild = "",
      orderByKey = "",
      orderByValue = "",
      orderByPriority = "";

    var limitToFirst = "",
      limitToLast = "",
      startAt = "",
      startAfter = "",
      endAt = "",
      endBefore = "",
      equalTo = "";

    if (dataIn.hasOwnProperty("orderByChild") && dataIn.orderByChild != "") {
      orderByChild = dataIn.orderByChild;
    }
    if (dataIn.hasOwnProperty("orderBy") && dataIn.orderBy != "") {
      orderByChild = dataIn.orderBy;
    }
    if (dataIn.hasOwnProperty("orderByKey") && dataIn.orderByKey != "") {
      orderByKey = dataIn.orderByKey;
    }
    if (dataIn.hasOwnProperty("orderByValue") && dataIn.orderByValue != "") {
      orderByValue = dataIn.orderByValue;
    }
    if (
      dataIn.hasOwnProperty("orderByPriority") &&
      dataIn.orderByPriority != ""
    ) {
      orderByPriority = dataIn.orderByPriority;
    }

    if (dataIn.hasOwnProperty("limitToFirst") && dataIn.limitToFirst != "") {
      limitToFirst = parseInt(dataIn.limitToFirst);
    }
    if (dataIn.hasOwnProperty("limitToLast") && dataIn.limitToLast != "") {
      limitToLast = parseInt(dataIn.limitToLast);
    }
    if (dataIn.hasOwnProperty("startAt") && dataIn.startAt != "") {
      startAt = dataIn.startAt;
    }
    if (dataIn.hasOwnProperty("startAfter") && dataIn.startAfter != "") {
      startAfter = dataIn.startAfter;
    }
    if (dataIn.hasOwnProperty("endAt") && dataIn.endAt != "") {
      endAt = dataIn.endAt;
    }
    if (dataIn.hasOwnProperty("endBefore") && dataIn.endBefore != "") {
      endBefore = dataIn.endBefore;
    }

    if (dataIn.hasOwnProperty("equalTo") && dataIn.equalTo != "") {
      equalTo = dataIn.equalTo;
    }

    if (orderByChild != "") {
      receivedRef = receivedRef.orderByChild(orderByChild);
    }
    if (orderByKey != "") {
      receivedRef = receivedRef.orderByKey();
    }
    if (orderByValue != "") {
      receivedRef = receivedRef.orderByValue();
    }
    if (orderByPriority != "") {
      receivedRef = receivedRef.orderByPriority(orderByPriority);
    }

    if (limitToFirst != "") {
      receivedRef = receivedRef.limitToFirst(limitToFirst);
    }
    if (limitToLast != "") {
      receivedRef = receivedRef.limitToLast(limitToLast);
    }
    if (startAt != "") {
      receivedRef = receivedRef.startAt(startAt);
    }
    if (startAfter != "") {
      receivedRef = receivedRef.startAfter(startAfter);
    }
    if (endAt != "") {
      receivedRef = receivedRef.endAt(endAt);
    }
    if (endBefore != "") {
      receivedRef = receivedRef.endBefore(endBefore);
    }

    if (equalTo != "") {
      receivedRef = receivedRef.equalTo(equalTo);
    }

    receivedRef.once("value", (snapshot) => {
      if (snapshot.val() !== null) {
        let formatter = {};
        if (dataIn.sortData) {
          let formatter = {};
          snapshot.forEach((child) => {
            // console.log(child.key+" "+child.val().lastUpdated);
            formatter[child.key] = child.val();
          });
          let newObj;
          if (dataIn.descendingOrder) {
            newObj = reverseObj(formatter);
          } else {
            newObj = formatter;
          }
          res.send({
            status: "success",
            message: "User data found",
            data: newObj,
          });
        } else {
          res.send({
            status: "success",
            message: "User data found",
            data: snapshot.val(),
          });
        }
      } else {
        res.send({
          status: "error",
          message: "No data found from getUserData",
          data: "",
        });
      }
    });
  } else {
    res.send({ status: "error", message: "Please enter all required fields" });
  }
});

app.post("/removeData", (req, res) => {
  let dataIn = req.body;

  console.log("removeData | ", dataIn);

  if (dataIn.hasOwnProperty("deleteFrom") && dataIn.deleteFrom != "") {
    rootRef.child(dataIn.deleteFrom).remove();

    res.send({ status: "success", message: "Child removed successfully" });
  } else {
    res.send({ status: "error", message: "Please enter all required fields" });
  }
});

app.post("/pushData", (req, res) => {
  let dataIn = req.body;

  console.log("pushData | ", dataIn);

  if (
    dataIn.hasOwnProperty("pushTo") &&
    dataIn.pushTo != "" &&
    dataIn.hasOwnProperty("data") &&
    dataIn.data != ""
  ) {
    var pushRef = rootRef.child(dataIn.pushTo);

    var insertedKey = pushRef.push(dataIn.data);

    res.send({
      status: "success",
      message: "Data pushed successfully",
      insertedKey: insertedKey.key,
      insertedData: dataIn.data,
    });
  } else {
    res.send({ status: "error", message: "Please enter all required fields" });
  }
});

app.post("/setData", (req, res) => {
  let dataIn = req.body;

  console.log("setData | ", dataIn);

  if (
    dataIn.hasOwnProperty("pushTo") &&
    dataIn.pushTo != "" &&
    dataIn.hasOwnProperty("data") &&
    dataIn.data != ""
  ) {
    var pushRef = rootRef.child(dataIn.pushTo);

    var insertedKey = pushRef.set(dataIn.data);

    res.send({
      status: "success",
      message: "Data pushed successfully",
      insertedData: dataIn.data,
    });
  } else {
    res.send({ status: "error", message: "Please enter all required fields" });
  }
});

app.post("/updateData", (req, res) => {
  let dataIn = req.body;

  console.log("updateData | ", dataIn);

  if (
    dataIn.hasOwnProperty("pushTo") &&
    dataIn.pushTo != "" &&
    dataIn.hasOwnProperty("data") &&
    dataIn.data != ""
  ) {
    var pushRef = rootRef.child(dataIn.pushTo);

    var insertedKey = pushRef.update(dataIn.data);

    res.send({
      status: "success",
      message: "Data pushed successfully",
      insertedData: dataIn.data,
    });
  } else {
    res.send({ status: "error", message: "Please enter all required fields" });
  }
});

app.post("/updateDeviceInfo", (req, res) => {
  let dataIn = req.body;

  if (
    dataIn.hasOwnProperty("userID") &&
    dataIn.hasOwnProperty("device_id") &&
    dataIn.userID != "" &&
    dataIn.device_id != ""
  ) {
    var userID = dataIn.userID,
      userDeviceId = dataIn.device_id;

    userRef
      .orderByChild("device_id/" + userDeviceId)
      .equalTo(false)
      .once(
        "value",
        function (snapshot) {
          console.log(snapshot.length);

          snapshot.forEach(function (child) {
            var userId = child.key;
            if (userId !== userID) {
              var refRemoveFalse = firebase
                .database()
                .ref("phone/users/" + userId + "/device_id/" + userDeviceId);
              refRemoveFalse.remove();
              console.log(new Date() + " Removed from " + userId);
            }
          });
        },
        function (errorObject) {
          console.log("The read failed: " + errorObject.code);
        }
      );

    userRef
      .orderByChild("device_id/" + userDeviceId)
      .equalTo(true)
      .once(
        "value",
        function (snapshot) {
          console.log("Inside Second");
          snapshot.forEach(function (child) {
            var userId = child.key;
            console.log(
              new Date() + "Second Device ID True | UserID: " + userId
            );
            if (userId !== userID) {
              var refRemoveFalse = firebase
                .database()
                .ref("phone/users/" + userId + "/device_id/" + userDeviceId);
              refRemoveFalse.remove();
              console.log(new Date() + " Removed from " + userId);
            }
          });
        },
        function (errorObject) {
          console.log("The read failed: " + errorObject.code);
        }
      );
  }

  res.send({
    "test-reply": "test-reply",
  });
});

function generateConversationId(conversationType, membersIds) {
  let conversationID = "";
  if (1 === conversationType) {
    conversationID = `1_${membersIds[0]}`;
  } else if (2 == conversationType) {
    let member_id1 = membersIds[0];
    let member_id2 = membersIds[1];

    //Don't use localeCompare because it uses some weird natural language sort
    //let str_comparison = member_id1.localeCompare(member_id2);
    //let str_comparison = -(member_id1 < member_id2) || +(member_id1 > member_id2);

    //if( str_comparison <= 0 ) {
    if (member_id1 < member_id2) {
      conversationID = member_id1 + "_" + member_id2;
    }
    //else if( str_comparison > 0 ) {
    else {
      conversationID = member_id2 + "_" + member_id1;
    }
    conversationID = "2_" + conversationID;
  } else if (3 == conversationType) {
    conversationID = "3_" + new Date().getTime().toString(16);
    conversationID = conversationID.replace(/\./g, "");
  }
  return conversationID;
}

function createConversation(conversationID, conversationParameters) {
  let date = +new Date();

  let banned_members = conversationParameters["banned_members"] || "";

  let createConversationParams = {
    count: Object.keys(conversationParameters["members"]).length,
    conversation_type: conversationParameters["conversation_type"],
    members: conversationParameters["members"],
    creator: conversationParameters["creator"],
    admins: conversationParameters["admins"],
    banned_members: banned_members,
    name: conversationParameters["name"],
    avatar: conversationParameters["avatar"],
    created_on: date,
    modified_on: date,
    lastUpdated: {
      ".sv": "timestamp",
    },
  };

  return db
    .ref(`/phone/conversations/${conversationID}`)
    .update(createConversationParams);
}

function createUsersConversation(convID, recentConv, message, membersIds) {
  let date = +new Date();

  let ucParams = {
    callID: recentConv["callID"],
    active: recentConv["active"],
    callerName: recentConv["callerName"],
    callerNumber: recentConv["callerNumber"],
    callerUID: recentConv["callerUID"],
    calledNumber: recentConv["calledNumber"],
    calledUID: recentConv["calledUID"],
    lastUpdated: { ".sv": "timestamp" },
    displayPriority: { ".sv": "timestamp" },
    menuSequence: recentConv["menuSequence"],
    options: recentConv["options"],
    recentChat: {
      counter: recentConv["recentChat"]["counter"],
      date: date,
      lastMessage: message,
    },
    count: membersIds.length,
    created_on: date,
    modified_on: date,
  };

  let updates = {};
  for (let i = 0; i < membersIds.length; i++) {
    updates[`/phone/users/${membersIds[i]}/conversations/${convID}`] = ucParams;
  }

  return db.ref().update(updates);
}

function createActiveConversation(convID, freeswitchIp, members) {
  callerProperties = null;
  fbFreeswitchIp = freeswitchIp.replace(/\./g, "*");
  memberAccountId = "";

  Object.keys(members).forEach(function (key) {
    let member = members[key];
    if ("caller" === member["leg_type"]) {
      callerProperties = member;
      memberAccountId = key;
    }
  });

  if (callerProperties) {
    let activeConvData = {
      leg_type: "caller",
      call_type: "PSTN",
      direction: "inbound",
      freeswitch_ip: freeswitchIp,
      freeswitch_request_queue: `/phone/queue/freeswitch/${fbFreeswitchIp}/tasks`,
      uuid: callerProperties["uuid"],
      account_id: memberAccountId,
      call_state: "IVR",
    };

    var updates = {};
    updates[
      `/phone/active_calls/${fbFreeswitchIp}/${convID}/${callerProperties["uuid"]}`
    ] = activeConvData;
    updates[`/phone/conversations/${convID}/active_call/${memberAccountId}`] =
      activeConvData;
    updates[`/phone/conversations/${convID}/lastUpdated`] = {
      ".sv": "timestamp",
    };

    return db.ref().update(updates);
  }
}

/**
 * Create conversation api.
 */
app.post("/create_conversation", (req, res) => {
  let result = {
    success: 0,
    error: 0,
    created: 0,
    conversationID: "",
    message: "",
  };

  try {
    let dataIn = JSON.parse(req.body.data);

    // Pulling from post parameters
    let membersIds = Object.keys(dataIn.conversation.members);
    let conversationType = dataIn.conversation.conversation_type;
    let conversationID = generateConversationId(conversationType, membersIds);

    if (!conversationID.trim()) {
      result.error = 1;
      result.message =
        "Invalid conversation ID. Please check your conversation type and conversation members.";
      res.send({ data: result });
    } else {
      db.ref(`/phone/conversations/${conversationID}/creator`)
        .once("value")
        .then(function (snapshot) {
          let data = snapshot.val() || "";
          if (!data) {
            console.log("We need to create a call here!");
            createConversation(conversationID, dataIn.conversation)
              .then((ccResult) => {
                console.log("Call is created!");
                return createUsersConversation(
                  conversationID,
                  dataIn.recent_conversation,
                  dataIn.message,
                  membersIds
                );
              })
              .then((cucResult) => {
                console.log("Created call for all users/participants");
                return createActiveConversation(
                  conversationID,
                  dataIn.freeswitch_ip,
                  dataIn.conversation.members
                );
              })
              .then((cacResult) => {
                console.log("Active call created!");
                console.log("Sending results");
                console.log("-------------------");

                result.success = 1;
                result.created = 1;
                result.conversationID = conversationID;
                result.message = "Conversation created successfully.";
                res.send({ data: result });
              });
          } else {
            console.log("Call already exists, lets return id of the call");

            result.success = 1;
            result.conversationID = conversationID;
            result.message = `ConversationID already exists: ${conversationID}`;
            res.send({ data: result });
          }
        });
    }
  } catch (error) {
    console.log("Error happened while creating conversation!");
    console.error(error);
    result.error = 1;
    result.message = "Error happened while creating conversation!";
    res.send({ data: result });
  }
});

/**
 * Update conversation api.
 */
app.post("/update_conversation", (req, res) => {
  let result = {
    success: 0,
    error: 0,
    conversationID: "",
    message: "",
  };

  try {
    let date = +new Date();
    let dataIn = JSON.parse(req.body.data);

    conversationID = dataIn["conversation_id"];
    calleeAccount = dataIn["callee_account"];

    usersConversation = {
      callID: dataIn["callID"],
      //active: dataIn['active'], //EC: Do not update active state
      callerName: dataIn["callerName"],
      lastUpdated: {
        ".sv": "timestamp",
      },
      displayPriority: {
        ".sv": "timestamp",
      },
      callerNumber: dataIn["callerNumber"],
      calledNumber: dataIn["calledNumber"],
      callerUID: dataIn["callerUID"],
      menuSequence: dataIn["menuSequence"],
      // options: dataIn['options'], //EC: Do not update options
      modified_on: date,
    };

    let updates = {};
    Object.keys(dataIn.members).forEach(function (key) {
      let member = dataIn.members[key];
      if (calleeAccount === member) {
        //Only update the callee so that if caller is a user then caller's conversation info doesn't get wiped out
        updates[`/phone/users/${key}/conversations/${conversationID}`] =
          usersConversation;
      }
    });

    db.ref()
      .update(updates)
      .then((updateRes) => {
        createActiveConversation(
          conversationID,
          dataIn.freeswitch_ip,
          dataIn.members
        );

        result.success = 1;
        result.conversationID = conversationID;
        result.message = "Updated successfully";
        res.send({ data: result });
      });
  } catch (error) {
    console.log("Error happened while updating conversation!");
    console.error(error);
    result.error = 1;
    result.message = "Error happened while updating conversation!";
    res.send({ data: result });
  }
});

/**
 * Get endpoint.
 * This method is going to recevive get requests with path and query for the firebase
 * It will execute firebase request and will return data.
 */
app.post("/get", logMiddleware, (req, res) => {
  db.ref(req.body.path)
    .once("value")
    .then(function (snapshot) {
      let data = snapshot.val() || {};

      console.log("Fetched data:");
      console.log(data);
      console.log("----------------------");

      res.send({ data: data });
    });
});

/**
 * PUT method.
 * This method is going to receive put request with path and data to put in firebase.
 */
app.post("/put", logMiddleware, (req, res) => {
  res.send({ data: {} });

  /*db.ref(req.body.path)
        .orderByKey() // order by chlidren's keys
        .limitToLast(1) // only get the last child
        .once('child_added', function(snapshot) {
            var key = snapshot.key;
            var val = snapshot.val();
            res.send({ 'data': val });
        },
        function(error) {
            // The callback failed.
            console.error(error);
            res.send({ 'data': {} });
        });
    */
});

/*
    tests time of 10 sequential db writes
*/
app.post("/test-db-write-seq", async (req, res) => {
  /*
        delete previous created test objects
    */
  console.log("Clearing objects...");
  const removePromises = [];
  for (let i = 0; i < 10; i++) {
    const obj = {
      id: `test-object-${i}`,
    };
    const removePromise = db
      .ref(`test-objects/test-object-${i}`)
      .remove((error) => {
        if (error) {
          console.log("Failed to remove object", i);
        } else {
          console.log("Removed object", i);
        }
      });
    removePromises.push(removePromise);
  }
  await Promise.all(removePromises);
  console.log("Objects cleared");

  /*
        write test objects
    */
  console.log("Writing objects...");
  console.time("test-db-write-seq");
  for (let i = 0; i < 10; i++) {
    const obj = {
      id: `test-object-${i}`,
    };
    await db.ref(`test-objects/test-object-${i}`).set(obj, (error) => {
      if (error) {
        console.log("Failed to write object", i);
      } else {
        console.log("Wrote object", i);
      }
    });
  }
  console.timeEnd("test-db-write-seq");

  return res.json({
    success: true,
  });
});

/*
    tests time of 10 simultaneous db writes
*/
app.post("/test-db-write-promise", async (req, res) => {
  /*
        delete previous created test objects
    */
  console.log("Clearing objects...");
  const removePromises = [];
  for (let i = 0; i < 10; i++) {
    const obj = {
      id: `test-object-${i}`,
    };
    const removePromise = db
      .ref(`test-objects/test-object-${i}`)
      .remove((error) => {
        if (error) {
          console.log("Failed to remove object", i);
        } else {
          console.log("Removed object", i);
        }
      });
    removePromises.push(removePromise);
  }
  await Promise.all(removePromises);
  console.log("Objects cleared");

  console.log("Writing objects...");
  console.time("test-db-write-promise");
  const writePromises = [];
  for (let i = 0; i < 10; i++) {
    const obj = {
      id: `test-object-${i}`,
    };
    const writePromise = db
      .ref(`test-objects/test-object-${i}`)
      .set(obj, (error) => {
        if (error) {
          console.log("Failed to write object", i);
        } else {
          console.log("Wrote object", i);
        }
      });
    writePromises.push(writePromise);
  }
  await Promise.all(writePromises);
  console.timeEnd("test-db-write-promise");

  return res.json({
    success: true,
  });
});

/* ---------------- multirun route ---------------- */

const axios = require("axios").default;

class MultirunError extends Error {
  // errorCode;
  // message;
  // status;

  constructor(errorCode, message, status) {
    super(message);
    this.errorCode = errorCode;
    this.message = message;
    this.status = status;
  }
}

/*
    --------------------------------
    Request Body JSON:
    --------------------------------
    {
      sql: string,
      params?: Array<any>,
      options?: {
        timeout?: number,
        initialBufferSize?: number
      }
    }

    sql : string
      An SQL string that will be executed. 
      Can optionally be given parameter markers (?) and also given an array of values to bind to the parameters.

    params?: Array<any>
      An array of values to bind to the parameter markers, if there are any. 
      The number of values in this array must match the number of parameter markers in the sql statement.

    options?: object
      An object containing query options that affect query behavior. 
      Valid properties include: 
      
        timeout?: number
          The amount of time (in seconds) that the query will attempt to execute before returning to the application.

        initialBufferSize?: number
          Sets the initial buffer size (in bytes) for storing data from SQL_LONG* data fields. 
          Useful for avoiding resizes if buffer size is known before the call.

    --------------------------------
    Response Body: 
    --------------------------------
    {
      success: boolean,
      result: Array<object>,
      opData: {
        count: number,
        columns: Array<{
          name: string,
          dataType: number
        }>,
        statement: string,
        parameters: Array<any>,
      }
    }

    success: boolean
      Indicates whether the request is successful.

    result: Array<object>
      The query result as an array of rows.

    opData:
      Additional information about the result.
      Contains the following properties:

        count: number
            the number of rows affected by the statement or procedure. Returns the result from ODBC function SQLRowCount.
        
        columns: 
            a list of columns in the result set. This is returned in an array. 
            Each column in the array has the following properties:
        
                name: 
                    The name of the column
                    
                dataType: 
                    The data type of the column properties
        
        statement: 
            The statement used to return the result set
        
        parameters: 
            The parameters passed to the statement or procedure. 
            For input/output and output parameters, this value will reflect the value updated from a procedure.

    --------------------------------
    More Info:
    --------------------------------
    For more info visit: 
      https://github.com/markdirish/node-odbc#result-array
      https://github.com/markdirish/node-odbc#querysql-parameters-callback

*/
app.post("/mysql-query", async (req, res) => {
  console.log("/mysql-query");

  try {
    const body = req.body;

    // VALIDATE INPUTS

    // check if body exists
    if (!body) {
      throw new MultirunError("INVALID_INPUT", `Expected body but got nil`);
    }

    // check if property 'sql' is of type 'string'
    const sql = body.sql;
    if (typeof sql != "string") {
      throw new MultirunError(
        "INVALID_INPUT",
        `Invalid parameter 'sql', 'string' expected got: ${sql} (${typeof sql})`
      );
    }

    const params = body.params;
    if (params) {
      // check if property 'params' is of type 'Array'
      if (!Array.isArray(params)) {
        throw new MultirunError(
          "INVALID_INPUT",
          `Invalid parameter 'params', 'Array' expected got: ${params} (${typeof params})`
        );
      }
    }

    let options = body.options;
    if (options) {
      // check if property 'options' is of type 'object'
      if (typeof options != "object") {
        throw new MultirunError(
          "INVALID_INPUT",
          `Invalid property 'options', 'object' expected got: ${options} (${typeof options})`
        );
      }

      // check if options object contains valid properties
      const validOptionKeys = ["timeout", "initialBufferSize"];
      for (let key in options) {
        if (!validOptionKeys.includes(key)) {
          throw new MultirunError(
            "INVALID_INPUT",
            `Invalid option '${key}', expected one of: ${validOptionKeys}`
          );
        }
      }
    } else {
      options = {};
    }

    // EXECUTE QUERY
    try {
      //const result = await mysql.query(sql, params, options);
      const result = await mysql.query(sql, params, options);
      //console.log("result:", result);

      const resultArray = [];
      for (let element of result) {
        resultArray.push(element);
      }

      //console.log("resultArray:", resultArray);

      const opData = {
        count: result.count,
        //columns: result.columns,
        statement: result.statement,
        parameters: result.parameters,
      };

      return res.status(200).json({
        success: true,
        result: resultArray,
        opData: opData,
      });
    } catch (error) {
      console.log("query error:", error);
      throw new MultirunError(
        "QUERY_FAILED",
        `Query failed, because: ${error.message}`
      );
    }
  } catch (error) {
    const {
      errorCode = "UNKNOWN",
      message = "Unexpected error",
      status = 500,
      stack,
    } = error;

    return res.status(status).json({
      success: false,
      errorCode: errorCode,
      message: message,
      stack: stack,
    });
  }
});

app.post("/multirun", (req, res) => {
  console.log("/multirun actions:", req.body.actions);
  try {
    const body = req.body;

    // check if body exists
    if (!req.body) {
      throw new MultirunError("INVALID_INPUT", `Expected body but got nil`);
    }

    const requestPromises = [];
    const actions = req.body.actions;

    // check if property 'actions' is of type object
    if (typeof actions != "object") {
      throw new MultirunError(
        "INVALID_INPUT",
        `Expected property 'actions' to be of type object, got: ${actions} (${typeof actions})`
      );
    }

    // check if actions object is not empty
    if (Object.keys(actions).length == 0) {
      throw new MultirunError(
        "INVALID_INPUT",
        `Expected at least 1 action, got 0`
      );
    }

    let actionKey;
    let actionsValidationError;

    // validate actions
    const areActionsValid = Object.entries(actions).every(([key, action]) => {
      actionKey = key;

      const method = action.method;

      // check if property 'method' is of type string
      if (typeof method != "string") {
        actionsValidationError = `Expected property 'method' to be of type string, got ${method} (${typeof method})`;
        return false;
      }

      const path = action.path;

      // check if property 'path' is of type string
      if (typeof path != "string") {
        actionsValidationError = `Expected property 'path' to be of type string, got: ${path} (${typeof path})`;
        return false;
      }

      // check if property 'data' is of type object (if provided)
      const body = action.body;
      if (body && typeof body != "object") {
        actionsValidationError = `Expected property 'body' to be of type object, got: ${body} (${typeof body})`;
        return false;
      }

      // check if property 'headers' is of type object (if provided)
      const headers = action.headers;
      if (headers && typeof headers != "object") {
        actionsValidationError = `Expected property 'headers' to be of type object, got: ${headers} (${typeof headers})`;
        return false;
      }

      return true;
    });
    if (!areActionsValid) {
      throw new MultirunError(
        "INVALID_INPUT",
        `${actionsValidationError} for action ${actionKey}`
      );
    }

    for (let key in actions) {
      console.log("key", key);
      const action = actions[key];

      const requestPromise = axios
        .request({
          url: `http://localhost:${5050}${action.path}`,
          method: action.method,
          data: action.body,
          headers: action.headers,
        })
        .then((response) => {
          return {
            key: key,
            data: response.data,
            status: response.status,
            statusText: response.statusText,
          };
        })
        .catch((error) => {
          const response = error.response;
          return {
            key: key,
            data: response.data,
            status: response.status,
            statusText: response.statusText,
          };
        });

      requestPromises.push(requestPromise);
    }

    return Promise.all(requestPromises).then((results) => {
      const resultEntries = results.map((result) => {
        return [result.key, result];
      });
      const resultsObject = Object.fromEntries(resultEntries);
      return res.status(200).json({
        success: true,
        results: resultsObject,
      });
    });
  } catch (error) {
    const {
      errorCode = "UNKNOWN",
      message = "Unexpected error",
      status = 500,
      stack,
    } = error;

    return res.status(status).json({
      success: false,
      errorCode: errorCode,
      message: message,
      stack: stack,
    });
  }
});

/* ---------------- multirun test routes ---------------- */

app.post("/my-test-post", (req, res) => {
  return res.status(200).json({
    success: true,
    data: {
      message: "POST Result",
    },
    note: "-- This is the actual result object! --",
  });
});
app.get("/my-test-get", (req, res) => {
  return res.status(200).json({
    success: true,
    data: {
      message: "GET Result",
    },
    note: "-- This is the actual result object! --",
  });
});
app.put("/my-test-put", (req, res) => {
  return res.status(200).json({
    success: true,
    data: {
      message: "PUT Result",
    },
    note: "-- This is the actual result object! --",
  });
});
app.delete("/my-test-delete", (req, res) => {
  return res.status(200).json({
    success: true,
    data: {
      message: "DELETE Result",
    },
    note: "-- This is the actual result object! --",
  });
});

/* -------------------------------- */

app.listen(5050);

//console.log(require("util").inspect(app._router.stack, false, null, true /* enable colors */))
