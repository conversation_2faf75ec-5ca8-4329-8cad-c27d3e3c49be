config = {};

// FIREBASE DATABASE CONNECTION
config.firebase = {};
config.firebase.cert_path = './config/glowing-heat-9503-firebase-adminsdk-mqh8o-4f0e22b568.json';
config.firebase.database_url = 'https://glowing-heat-9503.firebaseio.com';


// --------------------------------
// DATABASE CONNECTION CONFIG
// --------------------------------
config.database = {};


// The DSN to use to connect to the database
//config.database.dsn = "phoneic_local_proxy";
config.database.dsn = "phoneic";

// The number of seconds to wait for a request on the connection to complete before returning to the application
config.database.connectionTimeout = 10;

// The number of seconds to wait for a login request to complete before returning to the application
config.database.loginTimeout = 10;


// --------------------------------
// CONNECTION POOL CONFIG
// --------------------------------

// The initial number of Connections created in the Pool
config.database.connectionPoolInitialSize = 10;

// How many additional Connections to create when all of the Pool's connections are taken
config.database.connectionPoolIncrementSize = 1;

// The maximum number of open Connections the Pool will create
config.database.connectionPoolMaxSize = 10; 

module.exports = config;
