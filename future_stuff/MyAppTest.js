const { MyApp, Action } = require("./MyApp");

const myApp = new MyApp();
global.myApp = myApp;

myApp.registerAction(
  new Action("test-success-action", (params) => {
    return Promise.resolve("Success!");
  })
);

myApp.registerAction(
  new Action("test-failure-action", (params) => {
    return Promise.reject("Failure!");
  })
);

myApp.registerAction(
  new Action("getData", (params) => {
    return Promise.resolve("");
  })
);

myApp.listen();
