const express = require("express");

class MyLogger {
  // prefix;
  // baseLogger;

  constructor(prefix, baseLogger) {
    this.prefix = prefix;
    this.baseLogger = baseLogger;
  }
  
  log(logType, ...params) {
    if (!logType || typeof logType != "string") {
      throw new Error(
        `Expected parameter 'logType' to be non-nil and of type string, got: ${logType}`
      );
    }

    const isLogTypeValid = ["info", "success", "warn", "error"].includes(
      logType
    );
    if (!isLogTypeValid) {
      throw new Error(
        "Expected parameter 'logType' to be one of [info, success, warn, error]"
      );
    }

    let logFunc;
    let logTypePrefix;

    if (logType == "info") {
      logFunc = console.log;
      logTypePrefix = "[🔵 INFO]";
    } else if (logType == "success") {
      logFunc = console.log;
      logTypePrefix = "[🟢 SUCCESS]";
    } else if (logType == "warn") {
      logFunc = console.warn;
      logTypePrefix = "[🟡 WARNING]";
    } else if (logType == "error") {
      logFunc = console.error;
      logTypePrefix = "[🔴 ERROR]";
    }

    let prefixesCombined = `[${this.prefix}]`;
    let curBaseLogger = this.baseLogger;
    while (curBaseLogger) {
      prefixesCombined = `[${curBaseLogger.prefix}] ` + prefixesCombined; 
      curBaseLogger = curBaseLogger.baseLogger
    }
    logFunc(`${prefixesCombined}`, logTypePrefix, ...params);
  }
}

class Action {
  // id;
  // func;
  // logger;

  constructor(id, func) {
    if (!id || typeof id != "string") {
      throw new Error(
        `Expected parameter 'id' to be non-nil and of type string, got: ${id} (${typeof id})`
      );
    }

    if (!func || typeof func != "function") {
      throw new Error(
        `Expected parameter 'func' to be non-nil and of type function, got: ${func} (${typeof func})`
      );
    }

    this.logger = new MyLogger(`Action-${id}`)

    this.id = id;
    this.func = func;
  }

  log(logType, ...params) {
    this.logger.log(logType, ...params)
  }

  async execute(params, refTag) {
    if (!params || typeof params != "object") {
      throw new Error(
        `Expected parameter 'params' to be non-nil and of type object, got: ${params} (${typeof params})`
      );
    }

    if (!refTag || typeof refTag != "string") {
      throw new Error(
        `Expected parameter 'refTag' to be non-nil and of type string, got: ${refTag} (${typeof refTag})`
      );
    }

    try {
      const result = await this.func(params)
      return new ActionResult(true, result, refTag);
    } catch (error) {
      return new ActionResult(false, error, refTag);
    }
  }
}

class ActionResult {
  // success;
  // resultOrError;
  // refTag;

  constructor(success, result, refTag) {
    this.success = success;
    this.resultOrError = result;
    this.refTag = refTag;
  }
}

class MyAppError extends Error {
  // statusCode;
  // errorCode;
  // message;

  constructor(statusCode, errorCode, message) {
    super(message);
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.message = message;
  }
}

class MyApp {
  // app;
  // actions = {};
  // logger;

  constructor() {
    this.logger = new MyLogger("MyApp");

    this.app = express();
    this.app.use(express.json());

    this.app.get("/", (req, res) => {
      res.send("ONLINE");
    });

    // app only has a single post method for all actions
    this.app.post("/app-action", (req, res, next) => {
      this.onAppAction(req, res, next);
    });
    this.app.use((err, req, res, next) => {
      this.errorHandler(err, req, res, next);
    });

    this.log("success", `[constructor] instance: ${this}`);
  }

  registerAction(action) {
    if (!action || !(action instanceof Action)) {
      throw new Error(
        `Expected parameter 'action' to be non-nil and of type Action, got: ${action}`
      );
    }

    this.actions[action.id] = action;

    this.log("success", `[registerAction] Registered action:`, action);
  }

  listen() {
    const port = process.env.PORT || 3000;
    this.app.listen(port, () => {
      this.log("success", `[listen] Listening on Port: ${port}`);
    });
  }

  log(logType, ...params) {
    this.logger.log(logType, ...params)
  }

  errorHandler(err, req, res, next) {
    const { statusCode = 500, errorCode = "UNKNOWN", message, stack } = err;
    this.log("error", `An error occured, error:`, err);
    res.status(statusCode).json({
      success: false,
      errorCode: errorCode,
      message: message,
    });
  }

  async onAppAction(req, res, next) {
    const funcLogger = new MyLogger("app-action", this.logger);

    try {
      const input = req.body;
      funcLogger.log("info", `input:`, input);

      /*
              check if body is available
          */
      if (!input) {
        throw new MyAppError(500, "INVALID_INPUT", "Expected body to exist");
      }

      /*
              check if actions array is available and valid
          */
      const inputActions = input.actions;
      const isInputActionsPropertyValid = inputActions && Array.isArray(inputActions);
      if (!isInputActionsPropertyValid) {
        throw new MyAppError(
          500,
          "INVALID_INPUT",
          `Expected property 'actions' to exist and be of type Array, got: ${inputActions} (${typeof inputActions})`
        );
      }

      /*
              check if all actions are valid
          */
      let inputActionsValidationError;
      const isInputActionsValid = inputActions.every((inputAction, inputActionIndex) => {
        const actionId = inputAction.id;
        const isActionIdValid = actionId && typeof actionId == "string";
        if (!isActionIdValid) {
          inputActionsValidationError = `Expected property 'id' to exist and be of type string on Input Action at index ${inputActionIndex}, got: ${actionId} (${typeof actionId})`;
          return false;
        }

        const actionParams = inputAction.params;
        const isActionParamsValid =
          actionParams && typeof actionParams == "object";
        if (!isActionParamsValid) {
          inputActionsValidationError = `Expected property 'params' to exist and be of type object on Input Action at index ${inputActionIndex}, got: ${actionParams} (${typeof actionParams})`;
          return false;
        }

        const refTag = inputAction.refTag;
        const isRefTagValid = refTag && typeof refTag == "string";
        if (!isRefTagValid) {
          inputActionsValidationError = `Expected property 'refTag' to exist and be of type object on Input Action at index ${inputActionIndex}, got: ${actionParams} (${typeof actionParams})`;
          return false;
        }

        const actionExists = this.actions[actionId];
        if (!actionExists) {
          inputActionsValidationError = `Action does not exist for Input Action at index ${inputActionIndex}, Action ID: ${actionId}`;
          return false;
        }

        return true;
      });
      if (!isInputActionsValid) {
        throw new MyAppError(500, "INVALID_INPUT", inputActionsValidationError);
      }

      const existingRefTags = {}
      const isInputActionsValid2 = inputActions.every((inputAction, inputActionIndex) => {
        const refTag = inputAction.refTag;
        const existingRefTagActionIndex = existingRefTags[refTag]
        if (existingRefTagActionIndex) {
          inputActionsValidationError = inputActionsValidationError = `Expected property 'refTag' to be unique for Input Action at index ${inputActionIndex} but, Input Action at index ${existingRefTagActionIndex} already defines it`;
          return false;
        }
        existingRefTags[refTag] = String(inputActionIndex)
        return true;
      }) 
      if (!isInputActionsValid2) {
        throw new MyAppError(500, "INVALID_INPUT", inputActionsValidationError);
      }

      funcLogger.log("info", "Executing actions...");

      const actionPromises = [];

      for (var inputAction of inputActions) {
        var { id, params, refTag } = inputAction;

        this.log("info", `Executing action -- refTag: ${refTag}`);

        const action = this.actions[id];
        const actionPromise = action.execute(params, refTag)
          .then(result => {
            if (result.success) {
              funcLogger.log("success", `Action Success -- refTag: ${result.refTag}`)
            } else {
              funcLogger.log("error", `Action Failed -- refTag: ${result.refTag}`)
            }
            return result;
          })
        actionPromises.push(actionPromise);
      }

      const actionResults = await Promise.all(actionPromises);
      const actionResultsObj = Object.fromEntries(
        actionResults.map((actionResult) => {
          return [actionResult.refTag, actionResult];
        })
      );

      funcLogger.log(
        "info",
        `All actions completed -- results:`,
        actionResults
      );

      return res.status(200).json({
        success: true,
        actionResults: actionResultsObj,
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = {
  Action,
  ActionResult,
  MyApp,
};
