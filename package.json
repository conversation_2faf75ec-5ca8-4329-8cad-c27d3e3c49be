{"name": "firebase-middleware", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon", "test-mysql": "node mysql.test.js"}, "author": "", "license": "ISC", "dependencies": {"aws-sdk": "^2.395.0", "axios": "^1.2.1", "benchmark": "^2.1.4", "body-parser": "^1.18.2", "express": "^4.16.3", "firebase-admin": "^9.11.0", "log-timestamp": "^0.2.1", "microtime": "^3.1.1", "node-fetch": "^3.3.0", "odbc": "^2.4.6"}, "devDependencies": {"nodemon": "^2.0.20"}}