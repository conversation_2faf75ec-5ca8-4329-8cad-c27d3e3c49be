﻿multirun route def


________________




Invoke
[POST] /multirun


________________




Request body example:
{
   "actions": [
       {
           "method": "post",
           "path": "/my-test-post",
           "body": {
               "dummyProperty": true
           }
       },
       {
           "method": "get",
           "path": "/my-test-get"
       },
       {
           "method": "put",
           "path": "/my-test-put",
           "body": {
               "dummyProperty": true
           }
       },
       {
           "method": "delete",
           "path": "/my-test-delete",
           "body": {
               "dummyProperty": true
           }
       }
   ]
}


Each action can define:
{
           "method": "...",
           "path": "/...",
           "body": {},
           "headers": {}
       }


________________




Response object example:
{
   "success": true,
   "results": [
       {
           "data": {
               "success": true,
               "data": {
                   "url": "/my-test-post",
                   "method": "POST",
                   "body": {
                       "dummyProperty": true
                   },
                   "params": {},
                   "query": {},
                   "headers": {
                       "accept": "application/json, text/plain, */*",
                       "content-type": "application/json",
                       "user-agent": "axios/1.2.1",
                       "content-length": "22",
                       "accept-encoding": "gzip, compress, deflate, br",
                       "host": "localhost:5050",
                       "connection": "close"
                   }
               }
           },
           "status": 200,
           "statusText": "OK"
       },
       {
           "data": {
               "success": true,
               "data": {
                   "url": "/my-test-get",
                   "method": "GET",
                   "body": {},
                   "params": {},
                   "query": {},
                   "headers": {
                       "accept": "application/json, text/plain, */*",
                       "user-agent": "axios/1.2.1",
                       "accept-encoding": "gzip, compress, deflate, br",
                       "host": "localhost:5050",
                       "connection": "close"
                   }
               }
           },
           "status": 200,
           "statusText": "OK"
       },
       {
           "data": {
               "success": true,
               "data": {
                   "url": "/my-test-put",
                   "method": "PUT",
                   "body": {
                       "dummyProperty": true
                   },
                   "params": {},
                   "query": {},
                   "headers": {
                       "accept": "application/json, text/plain, */*",
                       "content-type": "application/json",
                       "user-agent": "axios/1.2.1",
                       "content-length": "22",
                       "accept-encoding": "gzip, compress, deflate, br",
                       "host": "localhost:5050",
                       "connection": "close"
                   }
               }
           },
           "status": 200,
           "statusText": "OK"
       },
       {
           "data": {
               "success": true,
               "data": {
                   "url": "/my-test-delete",
                   "method": "DELETE",
                   "body": {
                       "dummyProperty": true
                   },
                   "params": {},
                   "query": {},
                   "headers": {
                       "accept": "application/json, text/plain, */*",
                       "content-type": "application/json",
                       "user-agent": "axios/1.2.1",
                       "content-length": "22",
                       "accept-encoding": "gzip, compress, deflate, br",
                       "host": "localhost:5050",
                       "connection": "close"
                   }
               }
           },
           "status": 200,
           "statusText": "OK"
       }
   ]
}


Response object without responses (response body shell)
{
   "success": true,
   "results": [
       {
           "data": {},
           "status": 200,
           "statusText": "OK"
       },
       {
           "data": {},
           "status": 200,
           "statusText": "OK"
       },
       {
           "data": {},
           "status": 200,
           "statusText": "OK"
       },
       {
           "data": {},
           "status": 200,
           "statusText": "OK"
       }
   ]
}


Each action result consists of:
{
           "data": {},
           "status": 200,
           "statusText": "OK"
       }