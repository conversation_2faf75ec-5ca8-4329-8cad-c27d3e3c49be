const measureExecutionTime = (label, func) => {
    return new Promise((resolve, reject) => {
        console.log("-- Running Benchmark:", label, "--")
        
        console.time(label)
        func()
            .then(() => {
                console.timeEnd(label)
                console.log("-- Done --\n\n")
                resolve()
            })
            .catch(error => {
                console.timeEnd(label)
                console.log("\terror:", error.message)
                console.log("-- Done --\n\n")
                resolve()
            })
    })
} 

module.exports = {  
    measureExecutionTime
}