const { measureExecutionTime } = require('./utils');
const axios = require('axios').default;

const runTests = async () => {
    await measureExecutionTime("Test", async () => {
        console.log("Hello!")
        await Promise.resolve()
    })
    
    await measureExecutionTime("Google Request", async () => {
        await axios.get("https://www.google.com/")
    })
    
    await measureExecutionTime("10 Google Requests", async () => {
        const getPromises = new Array(10)
            .fill(true)
            .map(() => axios.get("https://www.google.com/"));
        const results = await Promise.all(getPromises)
        const processed = results.map(result => {
            return result.status
        })
        console.log(processed)
    })
}

runTests()