const odbc = require("odbc");
const config = require("./config/config");

const databaseConfig = config.database;
if (!databaseConfig) {
  console.log("[mysql] databaseConfig is nil, using empty object");
  databaseConfig = {};
}

global.odbcConnectionPool = null;

const defaultPoolConfig = {
  connectionTimeout: 10,
  loginTimeout: 10,

  initialSize: 10,
  incrementSize: 10,
  maxSize: 100,
};

// wraps and extends an odbc pool instance
function wrapPool(pool) {
  // creates a fresh connection instead of re-using an existing one in the pool
  // make sure to first close an existing connection in the pool by calling close
  pool.createFreshConnection = async function () {
    console.log("[Pool] [createFreshConnection] Attempting to create connection...");
    try {
      const connectionConfig = this.connectionConfig;
      this.connectionsBeingCreatedCount++;

      const connection = await odbc.connect(connectionConfig);

      this.connectionsBeingCreatedCount--;
      this.poolSize++;

      connection.nativeClose = connection.close;
      connection.close = async function () {
        pool.increasePoolSize(1);
        try {
          await connection.nativeClose();
        } catch (error) {
          console.log("[Connection] [close] nativeClose failed", error);
        } finally {
          pool.poolSize--;
        }
      };

      console.log(
        "[Pool] [createFreshConnection] Successfully created connection!"
      );

      return connection;
    } catch (error) {
      console.log(
        "[Pool] [createFreshConnection] Failed to create connection",
        error
      );
      throw error;
    }
  };

  pool.queryWithRetry = async function (sql, params, options) {
    let connection;
    let result;
    let _error;

    // try initial query
    console.log("[Pool] [queryWithRetry] Attempting initial query...");
    try {
      // gets a free connection from the pool
      // note:
      // if freeConnections.length == 0 and pool.size < pool.maxSize then
      // this will trigger pool.increasePoolSize
      // and will return a new connection once it has been created
      // which is likely to be connected to the database
      connection = await this.connect();

      // perform initial query
      result = await connection.query(sql, params, options);
    } catch (error) {
      _error = error;
    }

    // if the initial query succeeds, return the result
    if (result) {
      console.log("[Pool] [queryWithRetry] Initial query successful!");

      if (connection) {
        try {
          connection.close();
        } catch {}
      }
      return result;
    }

    // if the initial query fails, retry
    if (_error) {
      console.log(
        "[Pool] [queryWithRetry] Initial query failed",
        _error,
        "Retrying..."
      );

      // close the existing connection
      if (connection) {
        try {
          // call nativeClose to call unwrapped, native close function
          await connection.nativeClose();
        } catch {
        } finally {
          // decrement pool size as the wrapped close function does
          this.poolSize--;
        }
      }

      console.log("[Pool] [queryWithRetry] Attempting retry query...");

      try {
        // create a fresh connection
        connection = await this.createFreshConnection();

        // perform retry query
        result = await connection.query(sql, params, options);
      } catch (error) {
        _error = error;
      }

      // if the retry query succeeds, return the result
      if (result) {
        console.log(
          "[Pool] [queryWithRetry] Retry query successful!",
        );

        if (connection) {
          try {
            connection.close();
          } catch {}
        }

        return result;
      }

      // if the retry query fails, cleanup and throw
      if (_error) {
        console.log(
          "[Pool] [queryWithRetry] Retry query successful! result:",
          result
        );

        if (connection) {
          try {
            connection.close();
          } catch {}
        }

        throw _error;
      } else {
        throw new Error("Retry query failed");
      }
    }
  };

  return pool;
}

async function createConnectionPool() {
  if (global.odbcConnectionPool) {
    return global.odbcConnectionPool;
  }

  console.log(
    "[mysql] [createConnectionPool] Invoked -- Creating Connection Pool"
  );

  const poolConfig = {
    connectionString: `DSN=${databaseConfig.dsn}`,

    connectionTimeout: databaseConfig.connectionTimeout,
    loginTimeout: databaseConfig.loginTimeout,

    initialSize: databaseConfig.connectionPoolInitialSize,
    incrementSize: databaseConfig.connectionPoolIncrementSize,
    maxSize: databaseConfig.connectionPoolMaxSize,

    shrink: false,
    reuseConnections: false,
  };

  console.log("[mysql] [createConnectionPool] poolConfig:", poolConfig);

  for (let key in defaultPoolConfig) {
    if (poolConfig[key] != undefined) {
      continue;
    }

    const defaultValue = defaultPoolConfig[key];
    poolConfig[key] = defaultValue;

    console.log(
      "[mysql] [createConnectionPool] Property",
      key,
      "is missing in config, assigning default value:",
      defaultValue
    );
  }

  const pool = await odbc.pool(poolConfig);

  const reuseConnections = this.reuseConnections;
  if (reuseConnections === undefined || reuseConnections === true) {
    this.reuseConnections = false;
    console.log(
      "[Pool] [ensureReuseConnectionsFalse] resuseConnections undefined or true, set to false"
    );
  } else {
    console.log(
      "[Pool] [ensureReuseConnectionsFalse] resuseConnections is false, all good!"
    );
  }

  const poolWrapped = wrapPool(pool);

  global.odbcConnectionPool = poolWrapped;

  console.log("[mysql] [createConnectionPool] Pool Created");

  return poolWrapped;
}

function getConnectionPool() {
  return global.odbcConnectionPool;
}

// query function wrapper
// for more info visit:
// https://github.com/markdirish/node-odbc#result-array
// https://github.com/markdirish/node-odbc#querysql-parameters-callback
async function query(sql, params, options) {
  //console.log("[mysql] [query] sql:", sql);

  // VALIDATE INPUTS

  // sql
  if (typeof sql != "string") {
    throw new Error(
      `Invalid parameter 'sql', 'string' expected got: ${sql} (${typeof sql})`
    );
  }

  // params
  if (params) {
    if (!Array.isArray(params)) {
      throw new Error(
        `[mysql] [query] Invalid parameter 'params', 'Array' expected got: ${params} (${typeof params})`
      );
    }
  } else {
    //console.log("[mysql] [query] Parameter 'params' not provided");
  }

  // options
  if (options) {
    if (typeof options != "object") {
      throw new Error(
        `[mysql] [query] Invalid property 'options', 'object' expected got: ${options} (${typeof options})`
      );
    }

    const validOptionKeys = ["timeout", "initialBufferSize"];
    for (let key in options) {
      if (!validOptionKeys.includes(key)) {
        throw new Error(
          `[mysql] [query] Invalid option '${key}', expected one of: ${validOptionKeys}`
        );
      }
    }
  } else {
    options = {};
    //console.log("[mysql] [query] Parameter 'options' not provided");
  }

  // CHECK IF CONNECTION POOL IS INITIALIZED
  const pool = getConnectionPool();
  if (!pool) {
    throw new Error("[mysql] [query] Connection Pool Not Initialized");
  }

  //console.log("[mysql] [query] Current Connection Pool Size:", pool.poolSize);

  const _options = {
    ...options,
  };

  //console.log("[mysql] [query] pool", pool);
  const result = await pool.queryWithRetry(sql, params, _options);

  return result;
}

// try {
//     createConnectionPool();
// } catch {
//     console.log("[mysql] Failed to Create Pool");
// }

module.exports = {
  createConnectionPool,
  getConnectionPool,
  query,
};
