{"info": {"_postman_id": "48744296-1b6a-4cd8-85fc-dd4d0be4d9c3", "name": "multirun", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "localhost:5050/multirun", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"actions\": {\n        \"k1\": {\n            \"method\": \"post\",\n            \"path\": \"/my-test-post\",\n            \"body\": { \n                \"dummyProperty\": true \n            },\n            \"headers\": {}\n        },\n        \"k2\": {\n            \"method\": \"get\",\n            \"path\": \"/my-test-get\"\n        },\n        \"k3\": {\n            \"method\": \"put\",\n            \"path\": \"/my-test-put\",\n            \"body\": { \n                \"dummyProperty\": true \n            }\n        },\n        \"k4\": {\n            \"method\": \"delete\",\n            \"path\": \"/my-test-delete\",\n            \"body\": { \n                \"dummyProperty\": true \n            }\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:5050/multirun", "host": ["localhost"], "port": "5050", "path": ["multirun"]}, "description": "multirun route"}, "response": []}]}