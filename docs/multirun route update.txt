﻿multirun route update


________________




Usage
Invoke route
[POST] /multirun


Request body example:
{
   "actions": {
       "k1": {
           "method": "post",
           "path": "/my-test-post",
           "body": {
               "dummyProperty": true
           },
           "headers": {}
       },
       "k2": {
           "method": "get",
           "path": "/my-test-get"
       },
       "k3": {
           "method": "put",
           "path": "/my-test-put",
           "body": {
               "dummyProperty": true
           }
       },
       "k4": {
           "method": "delete",
           "path": "/my-test-delete",
           "body": {
               "dummyProperty": true
           }
       }
   }
}
Response body example:
{
   "success": true,
   "results": {
       "k1": {
           "key": "k1",
           "data": {
               "success": true,
               "data": {
                   "message": "POST Result"
               },
               "note": "-- This is the actual result object! --"
           },
           "status": 200,
           "statusText": "OK"
       },
       "k2": {
           "key": "k2",
           "data": {
               "success": true,
               "data": {
                   "message": "GET Result"
               },
               "note": "-- This is the actual result object! --"
           },
           "status": 200,
           "statusText": "OK"
       },
       "k3": {
           "key": "k3",
           "data": {
               "success": true,
               "data": {
                   "message": "PUT Result"
               }
           },
           "status": 200,
           "statusText": "OK"
       },
       "k4": {
           "key": "k4",
           "data": {
               "success": true,
               "data": {
                   "message": "DELETE Result"
               },
               "note": "-- This is the actual result object! --"
           },
           "status": 200,
           "statusText": "OK"
       }
   }
}


________________




Failure Responses


🔴 No body is provided
{
   "success": false,
   "errorCode": "INVALID_INPUT",
   "message": "Expected property 'actions' to be of type object, got: undefined (undefined)"
}


🔴 Actions property is not specified, or of the correct type


Request body example:
{
   "action": {
       "k1": {
           "method": "post",
           "path": "/my-test-post",
           "body": {
               "dummyProperty": true
           },
           "headers": {}
       },
   }
}


Response body example:
{
   "success": false,
   "errorCode": "INVALID_INPUT",
   "message": "Expected property 'actions' to be of type object, got: undefined (undefined)"
}


🔴 Any individual action is invalid
Request body example:
{
   "actions": {
       "k1": {
           "metho": "post",
           "path": "/my-test-post",
           "body": {
               "dummyProperty": true
           },
           "headers": {}
       }
   }
}


Response body example:
{
   "success": false,
   "errorCode": "INVALID_INPUT",
   "message": "Expected property 'method' to be of type string, got undefined (undefined) for action k1"
}


________________




Request body example:
{
   "actions": {
       "k1": {
           "method": "post",
           "pat": "/my-test-post",
           "body": {
               "dummyProperty": true
           },
           "headers": {}
       }
   }
}


Response body example:
{
   "success": false,
   "errorCode": "INVALID_INPUT",
   "message": "Expected property 'path' to be of type string, got: undefined (undefined) for action k1"
}


________________




Request body example:
{
   "actions": {
       "k1": {
           "method": "post",
           "path": "/my-test-post",
           "body": "false",
           "headers": {}
       }
   }
}


Response body example:
{
   "success": false,
   "errorCode": "INVALID_INPUT",
   "message": "Expected property 'body' to be of type object, got: false (string) for action k1"
}


________________




Request body example
{
   "actions": {
       "k1": {
           "method": "post",
           "path": "/my-test-post",
           "body": {},
           "headers": "false"
       }
   }
}


Response body example
{
   "success": false,
   "errorCode": "INVALID_INPUT",
   "message": "Expected property 'headers' to be of type object, got: false (string) for action k1"
}