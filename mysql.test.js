const mysql = require("./mysql");

function createReadTest(numReads) {
  const readPromises = [];
  for (let i = 0; i <= numReads; i++) {
    const readPromise = mysql.query("SELECT * FROM sakila.staff;");
    readPromises.push(readPromise);
  }
  return Promise.all(readPromises);
}

const readTestUpperBound = 50;
const readTestInputs = new Array(readTestUpperBound)
  .fill(true)
  .map((value, i) => i + 1);
console.log("[mysql.test] readTestInputs:", readTestInputs);

function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

async function runTests() {
  console.log("----------------TESTS START----------------");
  
  const pool = await mysql.createConnectionPool();

  console.log("[mysql.test] [runTests] Waiting for connections to prime...");
  await delay(3000);

  for (let readTestInput of readTestInputs) {
    await delay(1000);
    const timeKey = `Read-Test-${readTestInput}-Reads`;
    console.time(timeKey);
    await createReadTest(readTestInput);
    console.timeEnd(timeKey);
    console.log("--");
  }

  console.log("----------------TESTS END----------------");
}

runTests();
